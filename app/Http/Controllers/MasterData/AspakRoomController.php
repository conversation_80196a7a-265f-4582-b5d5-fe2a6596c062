<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\AspakServiceRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AspakRoomController extends Controller
{
    public function index()
    {
        $title = "Ruangan";
        $breadcrumbs = ["Master Data", "Aspak", "Ruangan"];

        return view('master-data.aspak.room.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if ($request->ajax()) {
            // Get all data in flat structure for tree grid
            $allData = $this->getFlatTreeData($request);

            return response()->json([
                'data' => $allData,
                'recordsTotal' => count($allData),
                'recordsFiltered' => count($allData)
            ]);
        }
    }

    /**
     * Get flat tree data with hierarchy information
     */
    private function getFlatTreeData(
        Request $request
    )
    {
        $orderColumnIndex = $request->order[0]['column'];
        $orderColumn = $request->columns[$orderColumnIndex]['name'];
        $orderDir = $request->order[0]['dir'];

        $searchQuery = $request->search['value'];

        // Get all data ordered by hierarchy
        $allRooms = AspakServiceRoom::with('parent')
            ->orderBy($orderColumn, $orderDir);

        if ($searchQuery) {
            $allRooms->where(function ($query) use ($searchQuery) {
                $query->where('room_service_name', 'like', '%' . $searchQuery . '%')
                    ->orWhere('room_service_code', 'like', '%' . $searchQuery . '%');
            });
        }
        $allRooms = $allRooms->get();

        // Build hierarchy map
        $hierarchyMap = [];
        $rootItems = [];

        // First pass: identify root items and build parent-child map
        foreach ($allRooms as $room) {
            if ($room->parent_id === null) {
                $rootItems[] = $room;
            } else {
                if (!isset($hierarchyMap[$room->parent_id])) {
                    $hierarchyMap[$room->parent_id] = [];
                }
                $hierarchyMap[$room->parent_id][] = $room;
            }
        }

        // Build flat array with proper order
        $flatData = [];
        $index = 1;

        foreach ($rootItems as $root) {
            $this->addToFlatData($root, $flatData, $index, $hierarchyMap, 0);
        }

        return $flatData;
    }

    /**
     * Recursively add items to flat data array
     */
    private function addToFlatData($item, &$flatData, &$index, $hierarchyMap, $level)
    {
        $hasChildren = isset($hierarchyMap[$item->id]) && count($hierarchyMap[$item->id]) > 0;

        $flatData[] = [
            'DT_RowIndex' => $index++,
            'id' => $item->id,
            'parent_id' => $item->parent_id,
            'parent_name' => $item->parent ? $item->parent->room_service_name : '-',
            'room_service_name' => $item->room_service_name,
            'room_service_code' => $item->room_service_code,
            'type' => $item->type,
            'level' => $level,
            'has_children' => $hasChildren,
            'is_expanded' => true // Default expanded all
        ];

        // Add children recursively
        if ($hasChildren) {
            foreach ($hierarchyMap[$item->id] as $child) {
                $this->addToFlatData($child, $flatData, $index, $hierarchyMap, $level + 1);
            }
        }
    }

    public function edit(AspakServiceRoom $aspakServiceRoom)
    {
        if (!hasPermissionInGuard('Aspak Room - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            // Load parent relationship jika ada
            $aspakServiceRoom->load('parent');

            return response()->json([
                "data" => $aspakServiceRoom
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Data tidak ditemukan"
            ], 404);
        }
    }

    public function destroy(AspakServiceRoom $aspakServiceRoom)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            // Cek apakah item memiliki children
            $hasChildren = AspakServiceRoom::where('parent_id', $aspakServiceRoom->id)->exists();

            if ($hasChildren) {
                return response()->json([
                    "message" => "Data tidak dapat dihapus karena masih memiliki data anak"
                ], 422);
            }

            // Set deleted_by sebelum soft delete
            $aspakServiceRoom->deleted_by = getAuthUserId();
            $aspakServiceRoom->save();

            // Soft delete
            $aspakServiceRoom->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'room_service_code' => 'required|string|unique:aspak_service_rooms,room_service_code',
            'room_service_name' => 'required|string',
            'type' => 'required|in:GROUP,ROOM_SERVICE',
            'parent_id' => 'nullable|exists:aspak_service_rooms,id'
        ]);

        try {
            DB::beginTransaction();

            // Validasi parent_id harus GROUP jika diisi
            if ($request->parent_id) {
                $parent = AspakServiceRoom::find($request->parent_id);
                if (!$parent || $parent->type !== 'GROUP') {
                    return response()->json([
                        "message" => "Parent harus bertipe GROUP"
                    ], 422);
                }
            }

            AspakServiceRoom::create([
                'room_service_code' => $request->room_service_code,
                'room_service_name' => $request->room_service_name,
                'type' => $request->type,
                'parent_id' => $request->parent_id,
                'created_by' => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function create(Request $request)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            // Get available parents (only GROUP type)
            $parents = AspakServiceRoom::where('type', 'GROUP')
                ->orderBy('room_service_name', 'ASC')
                ->get(['id', 'room_service_name', 'parent_id']);

            $data = [
                'parents' => $parents,
                'parent_id' => $request->get('parent_id', null)
            ];

            return response()->json([
                "data" => $data
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, AspakServiceRoom $aspakServiceRoom)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'room_service_code' => 'required|string|unique:aspak_service_rooms,room_service_code,' . $aspakServiceRoom->id,
            'room_service_name' => 'required|string',
            'type' => 'required|in:GROUP,ROOM_SERVICE',
            'parent_id' => 'nullable|exists:aspak_service_rooms,id'
        ]);

        try {
            DB::beginTransaction();

            // Validasi parent_id harus GROUP jika diisi
            if ($request->parent_id) {
                $parent = AspakServiceRoom::find($request->parent_id);
                if (!$parent || $parent->type !== 'GROUP') {
                    return response()->json([
                        "message" => "Parent harus bertipe GROUP"
                    ], 422);
                }

                // Validasi tidak boleh memilih dirinya sendiri atau anaknya sebagai parent
                if ($request->parent_id == $aspakServiceRoom->id) {
                    return response()->json([
                        "message" => "Tidak dapat memilih diri sendiri sebagai parent"
                    ], 422);
                }

                // Cek apakah parent_id adalah anak dari item ini
                $isChild = $this->isChildOf($request->parent_id, $aspakServiceRoom->id);
                if ($isChild) {
                    return response()->json([
                        "message" => "Tidak dapat memilih anak sebagai parent"
                    ], 422);
                }
            }

            $aspakServiceRoom->update([
                'room_service_code' => $request->room_service_code,
                'room_service_name' => $request->room_service_name,
                'type' => $request->type,
                'parent_id' => $request->parent_id,
                'updated_by' => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Check if parentId is a child of itemId
     */
    private function isChildOf($parentId, $itemId)
    {
        $parent = AspakServiceRoom::find($parentId);
        if (!$parent) {
            return false;
        }

        if ($parent->parent_id == $itemId) {
            return true;
        }

        if ($parent->parent_id) {
            return $this->isChildOf($parent->parent_id, $itemId);
        }

        return false;
    }
}
