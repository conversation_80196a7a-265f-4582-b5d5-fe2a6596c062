<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Asset;
use App\Models\Document;
use App\Models\DocumentAsset;
use App\Models\DocumentAssetMedia;
use App\Models\Employee;
use App\Models\RequestDocument;
use App\Models\Room;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Yajra\DataTables\Facades\DataTables;

class AssetDocumentController extends Controller
{

    function index()
    {
        if (!hasPermissionInGuard('Penempatan & Document - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Penempatan dan Dokumen";
        $breadcrumbs = ["Manajemen Aset", "Penempatan dan Dokumen"];

        return view('asset-management.asset-document.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {

        // used in other role
        // if (!hasPermissionInGuard('Penempatan & Document - View')) {
        //     abort(403, "Unauthorized action.");
        // }

        if ($request->ajax()) {
            $data = Asset::with(["assetEntry", "item", "room"])
                ->where("category_type", "EQUIPMENT")
                ->leftJoin('rooms', 'rooms.id', '=', 'assets.document_room_id')
                ->when($request->type, function ($query) {
                    if (request("type") == "mutasi") {
                        $employeeId = auth()->guard('employee')->user()->id ?? null;
                        $userId = Auth::id() ?? null;
                        if (!$userId) {
                            $listRoomId = Room::where('pic_room', $employeeId)->pluck('id');
                            $employeeRole = auth()->guard('employee')->user()->role_id;
                            if ($listRoomId->isNotEmpty() && $employeeRole === 1) {
                                $listRoomId = Room::where('pic_room', $employeeId)->pluck('id');
                                $query->whereIn('assets.document_room_id', $listRoomId);
                            }
                        }
                    }
                })
                ->select('assets.*', 'rooms.room_name');

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("real_qr_code", function ($row) {
                    return $row->qr_code;
                })
                ->editColumn("qr_code", function ($row) {
                    return QrCode::size(30)->generate($row->qr_code);
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route("master-data.category.show", $row->id) . '"><i class="fas fa-edit"></i></a>';
                })
                ->addColumn("checklist", function ($row) {
                    $document = Document::with("documentAsset")->whereHas("documentAsset", function ($query) use ($row) {
                        $query->where("asset_id", $row->id);
                    })
                        ->where("document_type", "CHECKLIST")
                        ->select("document_path")
                        ->first();

                    return $row->documentAssets()->with("document")->whereHas("document", function ($query) {
                        $query->where("document_type", "CHECKLIST");
                    })->count() > 0 ? '<div class="text-center"><a href="#modal-document-preview" class="text-success list-docs" data-id="' . $row->id . '" data-type="CHECKLIST" data-bs-toggle="modal" title="Dokumen Ceklis Tersedia"><i class="fas fa-clipboard-check text-success"></i></a></div>' : '<div class="text-center"><i class="fas fa-clipboard text-muted" title="Dokumen Ceklis Belum Tersedia"></i></div>';
                })
                ->addColumn("penempatan", function ($row) {
                    $document = Document::with("documentAsset")->whereHas("documentAsset", function ($query) use ($row) {
                        $query->where("asset_id", $row->id);
                    })
                        ->where("document_type", "PENEMPATAN")
                        ->select("document_path")
                        ->first();

                    return $row->documentAssets()->with("document")->whereHas("document", function ($query) {
                        $query->where("document_type", "PENEMPATAN");
                    })->count() > 0 ? '<div class="text-center"><a href="#modal-document-preview" class="text-success list-docs" data-id="' . $row->id . '" data-type="PENEMPATAN" data-bs-toggle="modal" title="Dokumen Penempatan Tersedia"><i class="fas fa-map-marker-alt text-success"></i></a></div>' : '<div class="text-center"><i class="fas fa-map-marker text-muted" title="Dokumen Penempatan Belum Tersedia"></i></div>';
                })
                ->addColumn("pembayaran", function ($row) {
                    $document = Document::with("documentAsset")->whereHas("documentAsset", function ($query) use ($row) {
                        $query->where("asset_id", $row->id);
                    })
                        ->where("document_type", "PEMBAYARAN")
                        ->select("document_path")
                        ->first();

                    return $row->documentAssets()->with("document")->whereHas("document", function ($query) {
                        $query->where("document_type", "PEMBAYARAN");
                    })->count() > 0 ? '<div class="text-center"><a href="#modal-document-preview" class="text-success list-docs" data-id="' . $row->id . '" data-type="PEMBAYARAN" data-bs-toggle="modal" title="Dokumen Pembayaran Tersedia"><i class="fas fa-credit-card text-success"></i></a></div>' : '<div class="text-center"><i class="fas fa-credit-card text-muted" title="Dokumen Pembayaran Belum Tersedia"></i></div>';
                })
                ->addColumn("mutasi", function ($row) {
                    $document = Document::with("documentAsset")->whereHas("documentAsset", function ($query) use ($row) {
                        $query->where("asset_id", $row->id);
                    })
                        ->where("document_type", "MUTASI")
                        ->select("document_path")
                        ->first();

                    return $row->documentAssets()->with("document")->whereHas("document", function ($query) {
                        $query->where("document_type", "MUTASI");
                    })->count() > 0 ? '<div class="text-center"><a href="#modal-document-preview" class="text-success list-docs" data-id="' . $row->id . '" data-type="MUTASI" data-bs-toggle="modal" title="Dokumen Mutasi Tersedia"><i class="fas fa-exchange-alt text-success"></i></a></div>' : '<div class="text-center"><i class="fas fa-exchange-alt text-muted" title="Dokumen Mutasi Belum Tersedia"></i></div>';
                })
                ->addColumn("rusak", function ($row) {
                    $document = Document::with("documentAsset")->whereHas("documentAsset", function ($query) use ($row) {
                        $query->where("asset_id", $row->id);
                    })
                        ->where("document_type", "RUSAK")
                        ->select("document_path")
                        ->first();

                    return $row->documentAssets()->with("document")->whereHas("document", function ($query) {
                        $query->where("document_type", "RUSAK");
                    })->count() > 0 ? '<div class="text-center"><a href="#modal-document-preview" class="text-success list-docs" data-id="' . $row->id . '" data-type="RUSAK" data-bs-toggle="modal" title="Dokumen Aset Rusak Tersedia"><i class="fas fa-exclamation-triangle text-success"></i></a></div>' : '<div class="text-center"><i class="fas fa-exclamation-triangle text-muted" title="Dokumen Aset Rusak Belum Tersedia"></i></div>';
                })
                ->addColumn("foto", function ($row) {
                    $foto = DocumentAssetMedia::join("document_assets", "document_assets.id", "=", "document_asset_medias.document_asset_id")
                        ->join("documents", "documents.id", "=", "document_assets.document_id")
                        ->where(["document_assets.asset_id" => $row->id])
                        ->whereIn("document_type", ["PENEMPATAN", "MUTASI", "RUSAK"])
                        ->orderBy('document_asset_medias.id', 'DESC')
                        ->first();

                    return $foto ? '<img class="img-asset" src="' . asset("/storage/" . $foto->media_path) . '" width="50" style="cursor: pointer;">' : '-';
                })
                ->rawColumns(["action", "qr_code", "real_qr_code", "checklist", "penempatan", "pembayaran", "mutasi", "rusak", "foto"])
                ->make(true);
        }
    }

    function request_document(Request $request)
    {
        if (!hasPermissionInGuard('Penempatan & Document - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "template" => "required",
            "kode_barang" => "required|array",
            "kode_barang.*" => "required|exists:assets,id",
            "target_ruangan" => "required",
            "pihak_pertama" => "required",
            "pihak_kedua" => "required",
        ]);

        try {
            DB::beginTransaction();

            $mainPerson = [];
            $data = [];
            if ($request->template == "penempatan") {
                $template = "bast.penempatan";
                $fileName = "template_dokumen_penempatan";
                $documentCode = "PLA-" . now()->format("Ymd") . rand(100000, 999999);

                $mainPerson = [
                    'picItemStorage' => Employee::where('pic_type', 'PIC_PENYIMPANAN_BARANG')->first(),
                    'teamLeader' => Employee::where('pic_type', 'KETUA_TEAM_ASET')->first(),
                    'assetManager' => Employee::where('pic_type', 'PIC_PENGURUS_BARANG')->first(),
                    'headPlanning' => Employee::where('pic_type', 'KEPALA_PERENCANAAN_PROGRAM_ASET')->first(),
                ];

                $partyOne = Employee::where("pic_type", "PIC_PENGURUS_BARANG")->first();

                $data = Asset::join("items", "assets.item_id", "=", "items.id")
                    ->join("uoms", "assets.uom_id", "=", "uoms.id")
                    ->join("asset_entries", "assets.asset_entry_id", "=", "asset_entries.id")
                    ->leftJoin("rooms", "assets.document_room_id", "=", "rooms.id")
                    ->whereIn("assets.id", $request->kode_barang)
                    ->select(
                        "assets.id",
                        "assets.asset_code",
                        "assets.unit_price",
                        DB::raw("GROUP_CONCAT(assets.register_code) as register_codes"),
                        "items.item_name",
                        "items.item_code",
                        "rooms.room_code",
                        "rooms.room_name",
                        "uoms.uom_name",
                        DB::raw("COUNT(*) as total_quantity"),
                        "asset_entries.description",
                        "asset_entries.received_date"
                    )
                    ->groupBy("assets.unit_price", "items.item_code", "items.item_code", "rooms.room_code", "rooms.room_name", "uoms.uom_name")
                    ->get();
            } else if ($request->template == "mutasi") {
                $template = "bast.mutasi";
                $fileName = "template_dokumen_mutasi";
                $documentCode = "MUT-" . now()->format("Ymd") . rand(100000, 999999);

                $mainPerson = [
                    'assetManager' => Employee::where('pic_type', 'PIC_PENGURUS_BARANG')->first(),
                ];

                $room = Room::find(explode("_", $request->pihak_pertama)[1]);
                $partyOne = Employee::find($room->pic_room);

                $data = Asset::join("items", "assets.item_id", "=", "items.id")
                    ->join("asset_entries", "assets.asset_entry_id", "=", "asset_entries.id")
                    ->leftJoin("rooms", "assets.document_room_id", "=", "rooms.id")
                    ->whereIn("assets.id", $request->kode_barang)
                    ->select(
                        "assets.id",
                        "assets.asset_code",
                        "assets.qr_code",
                        "items.item_name",
                        "items.item_code",
                        "rooms.room_name",
                        "asset_entries.item_name as asset_entry_item_name",
                        "asset_entries.received_date",
                        DB::raw("1 as total_quantity")
                    )
                    ->get();
            } else if ($request->template == "rusak") {
                $template = "bast.rusak";
                $fileName = "template_dokumen_rusak";
                $documentCode = "DMG-" . now()->format("Ymd") . rand(100000, 999999);

                $room = Room::find(explode("_", $request->pihak_pertama)[1]);
                $partyOne = Employee::find($room->pic_room);

                $data = Asset::join("items", "assets.item_id", "=", "items.id")
                    ->join("asset_entries", "assets.asset_entry_id", "=", "asset_entries.id")
                    ->leftJoin("rooms", "assets.document_room_id", "=", "rooms.id")
                    ->whereIn("assets.id", $request->kode_barang)
                    ->select(
                        "assets.id",
                        "assets.asset_code",
                        "assets.qr_code",
                        "items.item_name",
                        "items.item_code",
                        "rooms.room_name",
                        "asset_entries.item_name as asset_entry_item_name",
                        "asset_entries.received_date"
                    )
                    ->get();
            }

            $partyTwo = Employee::find($request->pihak_kedua);

            // Validasi data yang diperlukan
            if (!$partyOne) {
                throw new \Exception("Pihak pertama tidak ditemukan");
            }
            
            if (!$partyTwo) {
                throw new \Exception("Pihak kedua tidak ditemukan");
            }
            
            if (empty($data)) {
                throw new \Exception("Data asset tidak ditemukan");
            }

            $requestDocument = RequestDocument::create([
                "document_code" => $documentCode,
                "document_type" => $request->template,
                "target_location" => $request->target_ruangan,
                "party1_id" => $partyOne->id,
                "party1_name" => $partyOne->employee_name,
                "party1_identification_number" => $partyOne->employee_identification_number,
                "party1_grade" => $partyOne->employee_grade,
                "party1_position" => $partyOne->employee_position,
                "party2_id" => $partyTwo->id,
                "party2_name" => $partyTwo->employee_name,
                "party2_identification_number" => $partyTwo->employee_identification_number,
                "party2_grade" => $partyTwo->employee_grade,
                "party2_position" => $partyTwo->employee_position,
            ]);

            foreach ($request->kode_barang as $key => $value) {
                $requestDocument->requestDocumentAssets()->create([
                    "asset_id" => $value
                ]);
            }

            DB::commit();

            $targetRoom = DB::table('rooms')
                ->leftJoin('request_documents', 'rooms.id', '=', 'request_documents.target_location')
                ->where('request_documents.id', $requestDocument->id)
                ->select('rooms.room_name')
                ->first();
            $initialRoom = DB::table('request_document_assets')
                ->join('assets', 'request_document_assets.asset_id', '=', 'assets.id')
                ->join('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->where('request_document_assets.request_document_id', $requestDocument->id)
                ->select('rooms.room_name')
                ->first();

            $pdf = Pdf::loadView($template, [
                "data" => $data,
                "requestDocument" => $requestDocument,
                "mainPerson" => $mainPerson,
                "targetRoom" => $targetRoom,
                "initialRoom" => $initialRoom
            ])->setPaper('a4', 'potrait');

            return $pdf->download($fileName . ".pdf");
        } catch (\Throwable $th) {
            DB::rollBack();
            
            // Jika request adalah AJAX, return JSON response
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $th->getMessage()
                ], 500);
            }
            
            return back()->with("error", $th->getMessage());
        }
    }

    function request_document_detail($documentCode)
    {
        if (!hasPermissionInGuard('Penempatan & Document - View')) {
            abort(403, "Unauthorized action.");
        }

        $requestDocument = RequestDocument::firstWhere(["document_code" => trim($documentCode)]);

        $assets = Asset::whereIn("id", $requestDocument->requestDocumentAssets()->pluck("asset_id"))
            ->with('item')
            ->get();

        return response()->json([
            "request_document" => $requestDocument,
            "data" => $assets
        ], 200);
    }

    function upload(Request $request)
    {
        if (!hasPermissionInGuard('Penempatan & Document - Action')) {
            abort(403, "Unauthorized action.");
        }

        // Normalize document code - trim dan uppercase
        $request->merge([
            'kode_dokumen' => strtoupper(trim($request->kode_dokumen))
        ]);

        // Validasi input
        $request->validate([
            "tipe" => "required|in:penempatan,mutasi,rusak,checklist,pembayaran,penempatan_lama",
            "kode_dokumen" => "required|string",
            "dokumen" => "required|file|mimes:pdf|max:10240", // Max 10MB
        ]);

        // Validasi tambahan untuk tipe tertentu
        if (in_array($request->tipe, ['checklist', 'pembayaran', 'penempatan_lama'])) {
            $request->validate([
                "kode_barang" => "required|array",
                "kode_barang.*" => "required|exists:assets,id",
            ]);
        }

        // Validasi untuk penempatan_lama
        if ($request->tipe == "penempatan_lama") {
            $request->validate([
                "ruangan_upload" => "required|exists:rooms,id",
            ]);
        }

        // Validasi document_code sudah ada atau belum
        $existingDocument = Document::where('document_code', $request->kode_dokumen)->first();
        if ($existingDocument) {
            throw new \Exception("Dokumen dengan kode '{$request->kode_dokumen}' sudah ada");
        }

        // Validasi untuk mencegah duplicate asset upload pada document_type yang sama
        $documentType = $request->tipe == 'penempatan_lama' ? 'PENEMPATAN' : strtoupper($request->tipe);
        
        $duplicateAssets = [];
        
        // Untuk tipe yang memerlukan kode_barang
        if (in_array($request->tipe, ['checklist', 'pembayaran', 'penempatan_lama']) && isset($request->kode_barang)) {
            foreach ($request->kode_barang as $assetId) {
                $existingDocumentAsset = DocumentAsset::whereHas('document', function($query) use ($documentType) {
                    $query->where('document_type', $documentType);
                })->where('asset_id', $assetId)->first();
                
                if ($existingDocumentAsset) {
                    $asset = Asset::with('item')->find($assetId);
                    $duplicateAssets[] = $asset->qr_code . ' - ' . $asset->item->item_name;
                }
            }
        }
        // Untuk tipe yang menggunakan request document (penempatan, mutasi, rusak)
        else if (in_array($request->tipe, ['penempatan', 'mutasi', 'rusak'])) {
            $requestDocument = RequestDocument::where('document_code', $request->kode_dokumen)->first();
            if ($requestDocument) {
                foreach ($requestDocument->requestDocumentAssets as $docAsset) {
                    $existingDocumentAsset = DocumentAsset::whereHas('document', function($query) use ($documentType) {
                        $query->where('document_type', $documentType);
                    })->where('asset_id', $docAsset->asset_id)->first();
                    
                    if ($existingDocumentAsset) {
                        $asset = Asset::with('item')->find($docAsset->asset_id);
                        $duplicateAssets[] = $asset->qr_code . ' - ' . $asset->item->item_name;
                    }
                }
            }
        }
        
        if (in_array($request->tipe, ['checklist', 'pembayaran', 'penempatan_lama'])) {
            if (!empty($duplicateAssets)) {
                throw new \Exception("Asset berikut sudah memiliki dokumen " . $documentType . ": " . implode(', ', $duplicateAssets));
            }
        }

        try {
            DB::beginTransaction();

            $requestDocument = null;
            $file_docs = $request->file("dokumen");

            // Validasi file dokumen
            if (!$file_docs) {
                throw new \Exception("File dokumen tidak ditemukan");
            }

            // Set folder berdasarkan tipe
            $folderMap = [
                "penempatan" => "bast/penempatan",
                "mutasi" => "bast/mutasi", 
                "rusak" => "bast/rusak",
                "checklist" => "bast/checklist",
                "pembayaran" => "bast/pembayaran",
                "penempatan_lama" => "bast/penempatan"
            ];

            $folder = $folderMap[$request->tipe] ?? "bast/other";

            // Upload file dokumen
            $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . rand(1000, 9999) . "." . $file_docs->extension(), 'public');

            if (in_array($request->tipe, ["checklist", "pembayaran", "penempatan_lama"])) {
                // Untuk tipe yang tidak memerlukan request document
                $document = Document::create([
                    "document_code" => $request->kode_dokumen,
                    "document_type" => $request->tipe == "penempatan_lama" ? "PENEMPATAN" : strtoupper($request->tipe),
                    "document_path" => $fileDocsUrl,
                    "created_by" => getAuthUserId()
                ]);

                foreach ($request->kode_barang as $key => $value) {
                    $documentAsset = $document->documentAsset()->create([
                        "asset_id" => $value,
                    ]);

                    // Handle photo uploads for penempatan_lama
                    if ($request->tipe == "penempatan_lama" && isset($request->file()['media_new'][$key])) {
                        foreach ($request->file()['media_new'][$key] as $file) {
                            $mediaUrl = $file->storeAs($folder . "/media-new", now()->format("YmdHis") . rand(1000, 9999) . "." . $file->extension(), 'public');
                            DocumentAssetMedia::create([
                                'document_asset_id' => $documentAsset->id,
                                'media_path' => $mediaUrl,
                            ]);
                        }
                    }

                    // Update asset room if specified
                    if ($request->tipe == "penempatan_lama" && $request->ruangan_upload) {
                        Asset::where('id', $value)->update([
                            'document_room_id' => $request->ruangan_upload
                        ]);
                    }
                }
            } else {
                // Untuk tipe yang memerlukan request document
                $requestDocument = RequestDocument::firstWhere(["document_code" => trim($request->kode_dokumen)]);
                
                if (!$requestDocument) {
                    throw new \Exception("Request document dengan kode '{$request->kode_dokumen}' tidak ditemukan");
                }

                $document = Document::create([
                    "document_code" => $requestDocument->document_code,
                    "document_type" => $requestDocument->document_type,
                    "document_path" => $fileDocsUrl,
                    "target_location" => $requestDocument->target_location,
                    "party1_id" => $requestDocument->party1_id,
                    "party1_name" => $requestDocument->party1_name,
                    "party1_identification_number" => $requestDocument->party1_identification_number,
                    "party1_grade" => $requestDocument->party1_grade,
                    "party1_position" => $requestDocument->party1_position,
                    "party2_id" => $requestDocument->party2_id,
                    "party2_name" => $requestDocument->party2_name,
                    "party2_identification_number" => $requestDocument->party2_identification_number,
                    "party2_grade" => $requestDocument->party2_grade,
                    "party2_position" => $requestDocument->party2_position,
                    "created_by" => getAuthUserId()
                ]);

                foreach ($requestDocument->requestDocumentAssets as $docAsset) {
                    $documentAssetId = $document->documentAsset()->create([
                        "asset_id" => $docAsset->asset_id,
                    ]);

                    $asset = Asset::find($docAsset->asset_id);
                    if (!$asset) {
                        throw new \Exception("Asset dengan ID {$docAsset->asset_id} tidak ditemukan");
                    }
                    
                    $asset->update(["document_room_id" => $requestDocument->target_location]);

                    foreach ($request->all() as $key => $media) {
                        if (str_starts_with($key, 'media_') && is_array($media)) {
                            $assetId = (int)str_replace('media_', '', $key);
                            if ($assetId === $docAsset->asset_id) {
                                foreach ($media as $file) {
                                    $mediaUrl = $file->storeAs($folder . "/media", $requestDocument->document_code . rand(100, 999) . "." . $file->extension(), 'public');
                                    DocumentAssetMedia::create([
                                        'document_asset_id' => $documentAssetId->id,
                                        'media_path' => $mediaUrl,
                                    ]);
                                }
                            }
                        }
                    }
                }

                $requestDocument->update(["document_id" => $document->id]);
            }

            DB::commit();
            
            // Jika request adalah AJAX, return JSON response
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Dokumen berhasil diupload'
                ], 200);
            }
            
            return back()->with("success", "Dokumen berhasil diupload");
        } catch (\Throwable $th) {
            DB::rollBack();
            
            // Jika request adalah AJAX, return JSON response
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $th->getMessage()
                ], 500);
            }
            
            return back()->with("error", $th->getMessage());
        }
    }

    function media(Asset $asset)
    {
        $medias = DocumentAssetMedia::with("documentAsset")
            ->whereHas("documentAsset", function ($query) use ($asset) {
                $query->where("asset_id", $asset->id);
            })
            ->get();

        return response()->json([
            "medias" => $medias
        ], 200);
    }

    function list_document(Asset $asset)
    {
        if (request()->ajax()) {
            $documents = Document::with("documentAsset")->whereHas("documentAsset", function ($query) use ($asset) {
                $query->where("asset_id", $asset->id);
            })
                ->where("document_type", request("type"))
                ->get();

            return view("components.asset-document.list_document", compact("documents"));
        }
    }
}
