<style>
    .menu-item {
        margin-bottom: 3px;
    }

    .menu-item .menu-link {
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .menu-item .menu-link:hover {
        background: rgba(45, 53, 60, 0.08);
    }

    .menu-item.active .menu-link {
        background: #2d353c;
        color: #fff;
    }

    .menu-item .menu-icon {
        width: 30px;
        text-align: center;
        margin-right: 10px;
    }

    .menu-item .menu-text {
        font-weight: 500;
    }

    /* Submenu Styling */
    .menu-submenu {
        padding-left: 44px;
        margin-top: 3px;
    }

    .menu-submenu .menu-item .menu-link {
        padding: 6px 16px;
    }

    .menu-item.has-sub .menu-caret {
        transition: all 0.2s ease;
    }

    .menu-item.has-sub.active .menu-caret {
        transform: rotate(90deg);
    }
</style>

@if(hasPermissionInGuard("Role - View"))
<div class="menu-item {{ request()->routeIs('master-data.role.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.role.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-user-shield"></i>
        </div>
        <div class="menu-text">Data Role</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Karyawan - View"))
<div class="menu-item {{ request()->routeIs('master-data.employee.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.employee.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-users"></i>
        </div>
        <div class="menu-text">Data Pegawai</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Program - View"))
<div class="menu-item {{ request()->routeIs('master-data.program.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.program.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-cogs"></i>
        </div>
        <div class="menu-text">Data Program</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data UOM - View"))
<div class="menu-item {{ request()->routeIs('master-data.uom.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.uom.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-ruler"></i>
        </div>
        <div class="menu-text">Data UOM</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Kategori Ruangan - View"))
<div class="menu-item {{ request()->routeIs('master-data.room-category.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.room-category.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-th-large"></i>
        </div>
        <div class="menu-text">Data Kategori Ruangan</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Ruangan - View"))
<div class="menu-item {{ request()->routeIs('master-data.room.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.room.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-building"></i>
        </div>
        <div class="menu-text">Data Ruangan</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Bidang - View"))
<div class="menu-item {{ request()->routeIs('master-data.division.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.division.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-th-large"></i>
        </div>
        <div class="menu-text">Data Bidang</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Kategori Barang - View"))
<div class="menu-item {{ request()->routeIs('master-data.category.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.category.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-tags"></i>
        </div>
        <div class="menu-text">Data Kategori Barang</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Barang - View"))
<div class="menu-item {{ request()->routeIs('master-data.item.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.item.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-box"></i>
        </div>
        <div class="menu-text">Data Barang</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Distributor - View"))
<div class="menu-item {{ request()->routeIs('master-data.distributor.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.distributor.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-truck"></i>
        </div>
        <div class="menu-text">Data Distributor</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Data Kategori Pemeliharaan - View"))
<div class="menu-item {{ request()->routeIs('master-data.maintenance-category.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.maintenance-category.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-tools"></i>
        </div>
        <div class="menu-text">Data Kategori Pemeliharaan</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard("Config Rekapitulasi - View"))
<div class="menu-item {{ request()->routeIs('master-data.config-stock-recap.*') ? 'active' : '' }}">
    <a href="{{ route('master-data.config-stock-recap.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-cog"></i>
        </div>
        <div class="menu-text">Config Rekapitulasi</div>
    </a>
</div>
@endif

{{-- ASPAK Menu with Submenu --}}
<div class="menu-item has-sub {{ request()->routeIs('master-data.aspak.*') ? 'active' : 'closed' }}">
    <a href="javascript:;" class="menu-link" onclick="toggleSubmenu(this)">
        <div class="menu-icon">
            <i class="fa fa-hospital"></i>
        </div>
        <div class="menu-text">ASPAK</div>
        <div class="menu-caret"></div>
    </a>
    <div class="menu-submenu" style="display: {{ request()->routeIs('master-data.aspak.*') ? 'block' : 'none' }};">
        <div class="menu-item {{ request()->routeIs('master-data.aspak.room.*') ? 'active' : '' }}">
            <a href="{{ route('master-data.aspak-room.index') }}" class="menu-link">
                <div class="menu-icon">
                    <i class="fa fa-door-open"></i>
                </div>
                <div class="menu-text">Rooms</div>
            </a>
        </div>
    </div>
</div>

<script>
    function toggleSubmenu(element) {
        const menuItem = element.closest('.menu-item');
        const submenu = menuItem.querySelector('.menu-submenu');

        if (menuItem.classList.contains('active')) {
            menuItem.classList.remove('active');
            if (submenu) {
                submenu.style.display = 'none';
            }
        } else {
            menuItem.classList.add('active');
            if (submenu) {
                submenu.style.display = 'block';
            }
        }
    }
</script>
