let idUpdateIncidental = 0;
let typeUpdate = "";

function updateForm(type, id) {
    idUpdateIncidental = id;
    typeUpdate = type;

    if (type === "accept_request") {
        acceptRequest(type, id);
    }
    if (type === "assign_request") {
        $('.btn-remove-officer').click();
        $("#modal-assign").modal("show");
        $("#btn-add-officer").click();
        $('#assign_notice').val("");
        $("#save-assign").prop("disabled", false);
    }
    if (type === "add_bhp") {
        $('.btn-remove-bhp').click();
        $("#modal-add-bhp").modal("show");
        $("#btn-add-bhp").click();
        $('#bhp_notice').val("");
        $("#save-bhp").prop("disabled", false);
    }
    if (type === "call_vendor") {
        $("#modal-call-vendor").modal("show");
        $("#save-vendor").prop("disabled", false);
        $('#vendor_notice').val("");
    }
    if (type === "final_decision") {
        $("#modal-final-decision").modal("show");
        $("#save-decision").prop("disabled", false);
        $('#decision_notice').val("");
        $('#final_decision_select').val("FIXED");
    }
}

/* Final DECISION */
$("#save-decision").on("click", function () {
    $("#save-decision").prop("disabled", true);
    $.ajax({
        url: `keputusan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            decision_notice: $('#decision_notice').val(),
            final_decision_select: $('#final_decision_select').val(),
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-final-decision").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-decision").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});
/* End of Final DECISION */

/* Call VENDOR */
$("#save-vendor").on("click", function () {
    $("#save-vendor").prop("disabled", true);
    $.ajax({
        url: `keputusan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            vendor_notice: $('#vendor_notice').val(),
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-call-vendor").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-vendor").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});
/* End of Call VENDOR */

/* ADD BHP */
$("#save-bhp").on("click", function () {
    const data = [];
    $("#table-bhp tr").each(function () {
        const name = $(this).find(".bhp-name").val();
        const price = $(this).find(".bhp-price").val();
        const qty = $(this).find(".bhp-qty").val();
        if (name || price || qty) {
            if (name) {
                data.push({
                    name: name || '',
                    price: parseInt(price.replace(/,/g, '')) || 0,
                    qty: parseInt(qty.replace(/,/g, '')) || 0,
                });
            }
        }
    });
    $("#save-bhp").prop("disabled", true);
    $.ajax({
        url: `keputusan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            bhp_notice: $('#bhp_notice').val(),
            data_bhp: data,
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-add-bhp").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-bhp").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});

$("#table-bhp").on("click", ".btn-remove-bhp", function () {
    $(this).closest("tr").remove();
});

$("#btn-add-bhp").on("click", function () {
    let newRow = `
            <tr>
                <td><input type="text" name="name[]" class="form-control bhp bhp-name" value="" placeholder="Nama"></td>
                <td><input type="text" name="price[]" class="form-control bhp bhp-price" value="0" placeholder="Harga"></td>
                <td><input type="text" name="qty[]" class="form-control bhp bhp-qty" value="0" placeholder="Kuantitas"></td>
                <td class="text-center"><button type="button" class="btn btn-danger btn-sm btn-remove-bhp"><i class="fa fa-times"></i></button></td>
            </tr>
        `;
    let $newRow = $(newRow);
    $("#table-bhp").append($newRow);

    $newRow.find('.bhp-price, .bhp-qty').each(function () {
        new AutoNumeric(this, {digitGroupSeparator: ',', decimalPlaces: 0});
    });
});
/* END of ADD BHP */

/* ASSIGN FUNCTION */
$("#save-assign").on("click", function () {
    const data_officer = [];
    $('.dropdown-officer').each(function () {
        const value = $(this).val();
        const text = $(this).find('option:selected').text();
        data_officer.push({
            id: value,
            name: text
        });
    });
    const ids = data_officer.map(officer => officer.id);
    const hasDuplicate = ids.some((id, index) => ids.indexOf(id) !== index);
    if (hasDuplicate) {
        return alert('Duplikat petugas ditemukan!');
    }

    $("#save-assign").prop("disabled", true);
    $.ajax({
        url: `keputusan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            assign_notice: $('#assign_notice').val(),
            data_officer: data_officer,
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-assign").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-assign").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});

$("#table-officer").on("click", ".btn-remove-officer", function () {
    $(this).closest("tr").remove();
});

$("#btn-add-officer").on("click", function () {
    let newRow = `
            <tr>
                <td style="min-width: 300px !important;"><select style="max-width: 300px !important;" name="employee_id[]" class="form-select dropdown-officer"></select></td>
                <td class="text-center"><button type="button" class="btn btn-danger btn-sm btn-remove-officer"><i class="fa fa-times"></i></button></td>
            </tr>
        `;

    let $newRow = $(newRow);
    $("#table-officer").append($newRow);
    initializeSelect2Officer($newRow.find('.dropdown-officer'));
});

function initializeSelect2Officer($element) {
    $element.select2({
        ajax: {
            url: "/dropdown",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    category: "OFFICER",
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.employee_name
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: 'Pilih Data Petugas',
        minimumInputLength: 0,
        allowClear: true,
        width: '100%',
        dropdownParent: $('#modal-assign')
    });
}

/* END ASSIGN FUNCTION */

/* ACCEPT FUNCTION */
function acceptRequest(type, id) {
    swal({
        title: 'Konfirmasi Proses Request?',
        text: 'Anda yakin akan melakukan konfirmasi terhadap proses request',
        buttons: {
            cancel: {text: 'Batal', visible: true, className: 'btn btn-default'},
            confirm: {text: 'Konfirmasi', className: 'btn btn-primary'}
        }
    }).then((isConfirm) => {
        if (isConfirm) {
            $.ajax({
                url: `keputusan/detail/${id}`,
                type: "PUT",
                data: {
                    _method: "PUT",
                    type_update: type
                },
                success: function (response) {
                    successMessage(response.message);
                    $("#datatable").DataTable().ajax.reload();
                },
                error: function (xhr) {
                    errorMessage(xhr?.responseJSON?.message || "Server error")
                }
            })
        }
    });
}

/* END ACCEPT FUNCTION */
