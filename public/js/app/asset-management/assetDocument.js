$(document).ready(function () {
    /* Data Table */
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/manajemen-aset/dokumen-aset-list",
            data: function (d) {
                d.filter_type = $("#filter_type").val();
            },
        },
        columns: [
            {
                data: "DT_RowIndex",
                name: "DT_RowIndex",
                orderable: false,
                searchable: false,
            },
            { data: "foto", name: "foto" },
            {
                data: "qr_code",
                name: "qr_code",
                render: function (v, t, r) {
                    return `<div class="text-center qr-code-container" style="cursor: pointer;" data-qr-code="${r.real_qr_code}">
                        ${r.qr_code}
                    </div>`;
                },
                searchable: true,
            },
            {
                data: "item.item_name",
                name: "item.item_name",
                render: function (v, t, r) {
                    return `${r.item.item_name} <br> <span class="text-danger">${r.asset_name}</span>`;
                },
            },
            { data: "item.item_code", name: "item.item_code" },
            {
                data: "register_code",
                name: "register_code",
                render: function (v, t, r) {
                    return `${r.register_code} <br> <span class="text-danger">${r.serial_number}</span>`;
                },
            },
            { data: "room_name", name: "room_name", orderable: false, searchable: false },
            { data: "checklist", name: "checklist" },
            { data: "penempatan", name: "penempatan" },
            { data: "pembayaran", name: "pembayaran" },
            { data: "mutasi", name: "mutasi" },
            { data: "rusak", name: "rusak" },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Initialize Select2 for filter dropdown
    $("#filter_type").select2({
        dropdownParent: $("#filterDrawer")
    });

    // Initialize tooltips for document icons using title attribute
    $('[title]').tooltip({
        trigger: 'hover',
        placement: 'top'
    });

    // Re-initialize tooltips after DataTable redraw
    $("#datatable").on("draw.dt", function () {
        $('[title]').tooltip({
            trigger: 'hover',
            placement: 'top'
        });
    });

    // Filter type change
    $("#filter_type").on("change", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    $("#datatable").on("click", ".doc-preview", function () {
        let path = $(this).data("path");
        let id = $(this).data("id");

        $.ajax({
            url: "/manajemen-aset/dokumen-aset/media/" + id,
            type: "GET",
            success: function (response) {
                let medias = response.medias;
                console.log(medias);

                $("#row-image").empty();
            },
        });

        $("#frame-doc").attr("src", "/storage/" + path);
    });

    $("#datatable").on("click", ".list-docs", function () {
        let id = $(this).data("id");
        let type = $(this).data("type");

        $.ajax({
            url: "/manajemen-aset/dokumen-aset/list-document/" + id,
            type: "GET",
            data: {
                type: type,
            },
            success: function (response) {
                $("#list-doc").html(response);
            }
        });
    });

    $("#datatable").on("click", ".img-asset", function () {
        let path = $(this).attr("src");
        let $modal = $("#modal-img-preview");
        let $img = $(".img-asset-preview");
        $modal.modal("show");
        $img.attr("src", path);
    });

    // Clean up modal when hidden
    $("#modal-img-preview").on("hidden.bs.modal", function () {
        let $img = $(".img-asset-preview");
        $img.removeClass("loading").off("load error");
        $img.attr("src", "");
    });

    /* Helper Book */
    $("#btn-helper-book").on("click", function () {
        $("#datatable-helper-book").DataTable({
            processing: true,
            serverSide: true,
            bDestroy: true,
            ajax: { url: "/manajemen-aset/buku-bantu-list" },
            columns: [
                {
                    name: "DT_RowIndex",
                    data: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                },
                {
                    name: "document_code",
                    data: "document_code",
                },
                {
                    name: "document_type",
                    data: "document_type",
                },
                {
                    name: "target_location.room_name",
                    data: "target_location.room_name",
                    render: function (data, type, row) {
                        return data || "-";
                    },
                },
                {
                    name: "action",
                    data: "action",
                },
            ],
        });
    });

    /* Item */
    function initializeSelect2Item($element) {
        let type = $("#template").val();
        let roomUpload = $("#ruangan_upload").val();
        let typeUpload = $("#tipe").val();
        let partyOne = type != "penempatan" ? $("#pihak_pertama")?.val()?.split("_")[1] : null;

        if (type == "penempatan") {
            menu = "alocation";
        }

        let selectedItems = [];
        $(".dropdown-asset").each(function () {
            let val = $(this).val();
            if (val) {
                selectedItems = selectedItems.concat(val);
            }
        });

        $element.select2({
            ajax: {
                url: "/dropdown/asset",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    let data = {
                        category_type: "EQUIPMENT",
                        pic: partyOne,
                        q: params.term || "",
                        page: params.page || 1,
                        roomId: roomUpload,
                        selectedItems: selectedItems
                    };

                    if (type === "penempatan") {
                        data.menu = "alocation";
                    }

                    return data;
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: $.map(data.data, function (item) {
                            return {
                                id: item.id,
                                text: item.qr_code + " - " + item.item_name,
                            };
                        }),
                        pagination: {
                            more: data.current_page < data.last_page,
                        },
                    };
                },
                cache: true,
            },
            placeholder: "Pilih Aset",
            minimumInputLength: 0,
            allowClear: true,
            width: "100%",
            dropdownParent: $element.parent(),
        });
    }

    $(document).on("change", ".dropdown-asset", function () {
        var asset_id = $(this).val();
        var row = $(this).closest("tr");



        if (asset_id) {
            row.find(".asset-id-input").val(asset_id);
            $.ajax({
                url: "/asset/" + asset_id,
                method: "GET",
                success: function (response) {
                    let asset = response.data;
                    row.find("td:nth-child(2)").text(asset.qr_code);
                    row.find("td:nth-child(3)").text(
                        asset.register_code + " / " + asset.serial_number,
                    );
                },
                error: function (xhr, status, error) {
                    console.error("Error fetching asset details:", error);
                    alert("An error occurred while fetching the asset details.");
                },
            });
        } else {
            row.find("td:nth-child(2)").text("");
            row.find("td:nth-child(3)").text("");
            row.find(".asset-id-input").val("");
        }
    });

    /* DOWNLOAD LOGIC */
    $("#btn-download-doc").on("click", function (e) {
        e.preventDefault();

        // Validasi form
        let isValid = true;
        let errorMessages = [];

        // Validasi template
        if (!$("#template").val()) {
            isValid = false;
            errorMessages.push("Template harus dipilih");
            $("#template").addClass("is-invalid");
        } else {
            $("#template").removeClass("is-invalid");
        }

        // Validasi target ruangan
        if (!$("#target_ruangan").val()) {
            isValid = false;
            errorMessages.push("Target ruangan harus dipilih");
            $("#target_ruangan").addClass("is-invalid");
        } else {
            $("#target_ruangan").removeClass("is-invalid");
        }

        // Validasi pihak pertama
        if (!$("#pihak_pertama").val()) {
            isValid = false;
            errorMessages.push("Pihak pertama harus dipilih");
            $("#pihak_pertama").addClass("is-invalid");
        } else {
            $("#pihak_pertama").removeClass("is-invalid");
        }

        // Validasi pihak kedua
        if (!$("#pihak_kedua").val()) {
            isValid = false;
            errorMessages.push("Pihak kedua harus dipilih");
            $("#pihak_kedua").addClass("is-invalid");
        } else {
            $("#pihak_kedua").removeClass("is-invalid");
        }

        // Validasi kode barang (asset)
        let selectedAssets = [];
        $("#table-download-doc .dropdown-asset").each(function () {
            let assetId = $(this).val();
            if (assetId) {
                selectedAssets.push(assetId);
            }
        });

        if (selectedAssets.length === 0) {
            isValid = false;
            errorMessages.push("Minimal satu asset harus dipilih");
            $("#table-download-doc").addClass("border-danger");
        } else {
            $("#table-download-doc").removeClass("border-danger");
        }

        // Tampilkan pesan error jika ada
        if (!isValid) {
            let errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            errorHtml += '<strong>Validasi Error:</strong><br>';
            errorMessages.forEach(function (message) {
                errorHtml += '• ' + message + '<br>';
            });
            errorHtml += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            errorHtml += '</div>';

            // Hapus alert sebelumnya jika ada
            $("#form-download-doc .alert-danger").remove();

            // Tambahkan alert di atas form
            $("#form-download-doc .modal-body").prepend(errorHtml);

            // Scroll ke atas modal
            $("#modal-download-doc .modal-body").scrollTop(0);
            return false;
        }

        // Jika valid, submit form dengan AJAX
        let formData = new FormData($("#form-download-doc")[0]);

        // Tampilkan loading state
        $("#btn-download-doc").prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i><span>Memproses...</span>');

        $.ajax({
            url: $("#form-download-doc").attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhrFields: {
                responseType: 'blob'
            },
            success: function (response, status, xhr) {
                // Buat blob URL untuk download
                let blob = new Blob([response]);
                let url = window.URL.createObjectURL(blob);

                // Dapatkan nama file dari header response
                let filename = 'template_dokumen.pdf';
                let disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    let matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }

                // Buat link untuk download
                let a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Tutup modal
                $("#modal-download-doc").modal('hide');

                // Tampilkan pesan sukses
                showSuccessMessage("Dokumen berhasil diunduh!");

                // Reset form
                clearDownloadForm();
            },
            error: function (xhr, status, error) {
                // Tampilkan pesan error
                let errorMessage = "Terjadi kesalahan saat mengunduh dokumen";

                // Coba parse response JSON
                try {
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        // Jika response bukan JSON, coba ambil dari responseText
                        let response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    }
                } catch (e) {
                    // Jika gagal parse JSON, gunakan pesan default
                    console.error("Error parsing response:", e);
                }

                showErrorMessage(errorMessage);
            },
            complete: function () {
                // Reset loading state
                $("#btn-download-doc").prop('disabled', false).html('<i class="fas fa-download me-1"></i><span>Download Dokumen</span>');
            }
        });
    });

    // Fungsi untuk menampilkan pesan sukses
    function showSuccessMessage(message) {
        // Hapus pesan sebelumnya
        $(".alert-success, .alert-danger").remove();

        let successHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Berhasil!</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Tambahkan pesan di atas halaman
        $("body").prepend(successHtml);

        // Auto hide setelah 5 detik
        setTimeout(function () {
            $(".alert-success").addClass('fade-out');
            setTimeout(function () {
                $(".alert-success").remove();
            }, 300);
        }, 5000);
    }

    // Fungsi untuk menampilkan pesan error
    function showErrorMessage(message) {
        // Hapus pesan sebelumnya
        $(".alert-success, .alert-danger").remove();

        let errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error!</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Tambahkan pesan di atas halaman
        $("body").prepend(errorHtml);

        // Auto hide setelah 5 detik
        setTimeout(function () {
            $(".alert-danger").addClass('fade-out');
            setTimeout(function () {
                $(".alert-danger").remove();
            }, 300);
        }, 5000);
    }

    // Fungsi untuk clear semua data form dan table
    function clearDownloadForm() {
        $("#template").val(null).trigger("change");
        $("#target_ruangan").val(null).trigger("change");
        $("#pihak_pertama").val(null).trigger("change");
        $("#pihak_kedua").val(null).trigger("change");
        $("#table-download-doc").empty();

        // Tambahkan empty state kembali
        $("#table-download-doc").html(`
            <tr id="empty-state" class="text-center">
                <td colspan="4" class="py-5 text-muted">
                    <i class="fas fa-inbox fa-2x mb-3 d-block text-muted"></i>
                    <p class="mb-0">Belum ada asset yang dipilih</p>
                    <small>Klik "Tambah Asset" untuk menambahkan asset</small>
                </td>
            </tr>
        `);

        // Hapus class is-invalid dari semua field
        $("#form-download-doc .is-invalid, #form-download-doc .is-valid").removeClass("is-invalid is-valid");
        $("#table-download-doc").removeClass("border-danger");

        // Hapus alert error yang ada di dalam modal
        $("#form-download-doc .alert-danger, #form-download-doc .alert-success").remove();

        // Reset button state
        $("#btn-download-doc").prop('disabled', false).html('<i class="fas fa-download me-1"></i><span>Download Dokumen</span>');
    }

    // Event ketika modal dibuka
    $("#modal-download-doc").on("show.bs.modal", function () {
        clearDownloadForm();
        
        // Clear all error and success states
        $("#form-download-doc .is-invalid, #form-download-doc .is-valid").removeClass("is-invalid is-valid");
        $("#form-download-doc .alert-danger, #form-download-doc .alert-success").remove();
        $("#table-download-doc").removeClass("border-danger");
    });

    // Event ketika modal ditutup
    $("#modal-download-doc").on("hidden.bs.modal", function () {
        // Clear form dan reset state
        clearDownloadForm();
        $("#btn-download-doc").prop('disabled', false).html('<i class="fas fa-download me-1"></i><span>Download Dokumen</span>');

        // Hapus alert error yang mungkin masih ada di dalam modal
        $("#form-download-doc .alert-danger, #form-download-doc .alert-success").remove();
    });

    $("#template").select2({
        placeholder: "Pilih Template",
        allowClear: true,
        width: "100%",
        dropdownParent: $("#template").parent(),
    });

    $("#template").on("change", function () {
        // Clear data ketika template berubah
        $("#pihak_pertama").val(null).trigger("change");
        $("#target_ruangan").val(null).trigger("change");
        $("#pihak_kedua").val(null).trigger("change");
        $("#table-download-doc").empty();
    });

    $("#target_ruangan").on("change", function () {
        let id = $(this).val();

        // Clear pihak kedua terlebih dahulu
        $("#pihak_kedua").val(null).trigger("change");

        // Clear table asset
        $("#table-download-doc").empty();

        if (id && $("#template").val() != "rusak") {
            $.ajax({
                url: "/master-data/data-ruangan/" + id,
                type: "GET",
                success: function (response) {
                    let room = response.data;

                    if (room.pic_room !== null) {
                        $("#pihak_kedua")
                            .append(
                                new Option(
                                    `${room.employee_name}`,
                                    room.pic_room,
                                    true,
                                    true,
                                ),
                            )
                            .trigger("change");
                    } else {
                        let newOption = new Option("", "", true, true);
                        $("#pihak_kedua").append(newOption).trigger("change");
                    }
                },
            });
        }
    });

    $("#btn-add-row-download").on("click", function () {
        // Hapus empty state jika ada
        $("#empty-state").remove();

        let newRow = `
            <tr>
                <td>
                    <select name="kode_barang[]" class="form-select dropdown-asset"></select>
                </td>
                <td>

                </td>
                <td>

                </td>
                <td class="text-center">
                    <button type="button" class="btn btn-danger btn-sm btn-remove-row-download">
                        <i class="fa fa-times"></i>
                    </button>
                </td>
            </tr>
        `;

        let $newRow = $(newRow);
        $("#table-download-doc").append($newRow);
        initializeSelect2Item($newRow.find(".dropdown-asset"));
    });

    $("#table-download-doc").on("click", ".btn-remove-row-download", function () {
        $(this).closest("tr").remove();

        // Jika tidak ada lagi row asset, tampilkan empty state
        if ($("#table-download-doc tr").length === 0) {
            $("#table-download-doc").html(`
                <tr id="empty-state" class="text-center">
                    <td colspan="4" class="py-5 text-muted">
                        <i class="fas fa-inbox fa-2x mb-3 d-block text-muted"></i>
                        <p class="mb-0">Belum ada asset yang dipilih</p>
                        <small>Klik "Tambah Asset" untuk menambahkan asset</small>
                    </td>
                </tr>
            `);
        }
    });

    $(".pihak_pertama").select2({
        ajax: {
            url: "/dropdown/employee",
            dataType: "json",
            delay: 250,
            data: function (params) {
                if ($("#template").val() == "penempatan") {
                    return {
                        q: params.term,
                        page: params.page || 1,
                        template: $("#template").val()
                    };
                } else {
                    return {
                        q: params.term,
                        page: params.page || 1,
                        pic_room: true
                    };
                }
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: $.map(data.data, function (item) {
                        const id = (item?.room_id) ? `${item.id}_${item.room_id}` : item.id;
                        const text = (item?.room_name) ? `${item?.employee_name} - ${item?.room_name}` : `${item?.employee_name}`;
                        return {
                            id: id,
                            text: text,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih Pihak Pertama",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $(".pihak_pertama").parent(),
    });

    $("#pihak_pertama").on("change", function () {
        let template = $("#template").val();

        // Clear table asset ketika pihak pertama berubah
        $("#table-download-doc").empty();

        if (template != "penempatan") {
            initializeSelect2Item($(".dropdown-asset"));
        }
    });

    $(".target_ruangan").select2({
        ajax: {
            url: "/dropdown/room",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page || 1,
                    template: $("#template").val()
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.room_code + " - " + item.room_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih Ruangan",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $(".target_ruangan").parent(),
    });

    $(".pihak_kedua").select2({
        ajax: {
            url: "/dropdown/employee",
            dataType: "json",
            delay: 250,
            data: function (params) {
                if ($("#template").val() == "rusak") {
                    return {
                        q: params.term,
                        page: params.page || 1,
                        template: $("#template").val()
                    };
                } else {
                    return {
                        q: params.term,
                        page: params.page || 1,
                        pic_room: true
                    };
                }
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text:
                                item.employee_identification_number +
                                " - " +
                                item.employee_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih Pihak Kedua",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $(".pihak_kedua").parent(),
    });

    // Event handler untuk clear table ketika pihak kedua berubah
    $("#pihak_kedua").on("change", function () {
        $("#table-download-doc").empty();
    });

    // Help button functionality
    $("#btn-help-download").on("click", function () {
        $("#modal-help-download").modal("show");
        // Add blur effect to download modal
        $("#modal-download-doc").addClass("modal-blur");
    });

    // Remove blur when help modal is closed
    $("#modal-help-download").on("hidden.bs.modal", function () {
        $("#modal-download-doc").removeClass("modal-blur");
    });

    /* UPLOAD LOGIC */

    // Form upload submit handler
    $("#form-upload-doc").on("submit", function (e) {
        e.preventDefault();

        // Validasi form - dipindahkan ke sini semua
        let isValid = true;
        let errorMessages = [];

        // Clear semua error state sebelumnya
        $("#form-upload-doc .is-invalid").removeClass("is-invalid");
        $("#table-upload-doc").removeClass("border-danger");
        $("#form-upload-doc .alert-danger").remove();

        // Validasi tipe dokumen
        if (!$("#tipe").val()) {
            isValid = false;
            errorMessages.push("Tipe dokumen harus dipilih");
            $("#tipe").addClass("is-invalid");
        }

        // Validasi kode dokumen
        if (!$("#kode_dokumen").val()) {
            isValid = false;
            errorMessages.push("Kode dokumen harus diisi");
            $("#kode_dokumen").addClass("is-invalid");
        }

        // Validasi file dokumen
        if (!$("#dokumen").val()) {
            isValid = false;
            errorMessages.push("File dokumen harus dipilih");
            $("#dokumen").addClass("is-invalid");
        } else {
            // Validasi tipe file
            let fileInput = $("#dokumen")[0];
            let file = fileInput.files[0];
            
            if (file) {
                // Validasi ekstensi file
                let allowedExtensions = ['pdf'];
                let fileName = file.name;
                let fileExtension = fileName.split('.').pop().toLowerCase();
                
                if (!allowedExtensions.includes(fileExtension)) {
                    isValid = false;
                    errorMessages.push("File dokumen harus berformat PDF");
                    $("#dokumen").addClass("is-invalid");
                } else if (file.size > 10 * 1024 * 1024) { // 10MB
                    isValid = false;
                    errorMessages.push("Ukuran file dokumen maksimal 10MB");
                    $("#dokumen").addClass("is-invalid");
                }
            }
        }

        // Validasi tambahan untuk tipe tertentu
        let tipe = $("#tipe").val();
        if (tipe === "checklist" || tipe === "pembayaran" || tipe === "penempatan_lama") {
            let selectedAssets = [];
            $("#table-upload-doc .dropdown-asset").each(function () {
                let assetId = $(this).val();
                if (assetId) {
                    selectedAssets.push(assetId);
                }
            });

            if (selectedAssets.length === 0) {
                isValid = false;
                errorMessages.push("Minimal satu asset harus dipilih");
                $("#table-upload-doc").addClass("border-danger");
            }
        }

        // Validasi untuk penempatan_lama
        if (tipe === "penempatan_lama") {
            if (!$("#ruangan_upload").val()) {
                isValid = false;
                errorMessages.push("Ruangan harus dipilih");
                $("#ruangan_upload").addClass("is-invalid");
            }
        }

        // Tampilkan pesan error jika ada
        if (!isValid) {
            let errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            errorHtml += '<strong>Validasi Error:</strong><br>';
            errorMessages.forEach(function (message) {
                errorHtml += '• ' + message + '<br>';
            });
            errorHtml += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            errorHtml += '</div>';

            // Tambahkan alert di atas form
            $("#form-upload-doc .modal-body").prepend(errorHtml);

            // Scroll ke atas modal
            $("#modal-upload-doc .modal-body").scrollTop(0);
            return false;
        }

        // Jika valid, submit form dengan AJAX
        let formData = new FormData(this);

        // Tampilkan loading state
        $("#btn-upload-doc").prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i><span>Memproses...</span>');

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                // Tutup modal
                $("#modal-upload-doc").modal('hide');

                // Tampilkan pesan sukses
                showSuccessMessage("Dokumen berhasil diupload!");

                // Reset form
                resetUploadForm();

                // Reload datatable jika ada
                if ($("#datatable").length) {
                    $("#datatable").DataTable().ajax.reload();
                }
            },
            error: function (xhr, status, error) {
                // Tampilkan pesan error
                let errorMessage = "Terjadi kesalahan saat upload dokumen";

                // Coba parse response JSON
                try {
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        let response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    }
                } catch (e) {
                    console.error("Error parsing response:", e);
                }

                // Tambahkan alert error di dalam modal
                let errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
                errorHtml += '<i class="fas fa-exclamation-triangle me-2"></i>';
                errorHtml += '<strong>Error!</strong> ' + errorMessage;
                errorHtml += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
                errorHtml += '</div>';

                $("#form-upload-doc .modal-body").prepend(errorHtml);

                // Scroll ke atas modal
                $("#modal-upload-doc .modal-body").scrollTop(0);
            },
            complete: function () {
                // Reset loading state
                $("#btn-upload-doc").prop('disabled', false).html('<i class="fas fa-upload me-1"></i><span>Upload Dokumen</span>');
            }
        });
    });

    $("#tipe").on("change", function () {
        let tipe = $(this).val();

        $("#table-upload-doc").empty();
        $("#kode_dokumen").val("");
        $("#dokumen").val("");

        $("#document_code_field").removeClass("d-none");
        $("#document_field").removeClass("d-none");

        // Hide asset list section by default
        $("#asset-list-section").addClass("d-none");

        if (tipe === "penempatan_lama") {
            $("#table-upload th:nth-child(4)").removeClass("d-none");
            $("#ruangan_field").removeClass("d-none");
            $("#btn-add-row-upload").removeClass("d-none");
            $("#table-upload").removeClass("d-none");
            $("#asset-list-section").removeClass("d-none");
        } else {
            $("#table-upload th:nth-child(4)").addClass("d-none");
            $("#ruangan_field").addClass("d-none");
            if (tipe === "checklist" || tipe === "pembayaran") {
                $("#btn-add-row-upload").removeClass("d-none");
                $("#table-upload").removeClass("d-none");
                $("#asset-list-section").removeClass("d-none");
            } else {
                $("#btn-add-row-upload").addClass("d-none");
                $("#table-upload").addClass("d-none");
                $("#asset-list-section").addClass("d-none");
            }
        }
    });

    $("#kode_dokumen").on("keyup", function (e) {
        e.preventDefault();
        let kode_dokumen = $(this).val();

        if (e.which === 13) {
            if (kode_dokumen.trim() === "") {
                return;
            }
            // Tampilkan loading state
            $(this).prop('disabled', true);
            
            $.ajax({
                url: "/manajemen-aset/dokumen-aset/request-document/" + kode_dokumen,
                method: "GET",
                success: function (response) {
                    let assets = response.data;
                    let type = response.request_document.document_type.toLowerCase();
                    
                    $("#tipe").val(type).trigger("change");
                    $("#kode_dokumen").val(response.request_document.document_code);

                    $("#table-upload").removeClass("d-none");
                    $("#asset-list-section").removeClass("d-none");

                    $("#table-upload-doc").empty();
                    let newRow = "";
                    $.each(assets, function (key, asset) {
                        newRow += `
                                <tr>
                                    <td>
                                        ${asset.qr_code} - ${asset.item.item_name}
                                    </td>
                                    <td>
                                        ${asset.qr_code}
                                    </td>
                                    <td>
                                        ${asset.register_code} / ${asset.serial_number}
                                    </td>
                                    <td class="text-center">
                                        <input type="file" name="media_${asset.id}[]" id="media" accept="image/*,video/*" class="form-control form-control-sm" multiple>
                                    </td>
                                </tr>
                            `;
                    });

                    $("#table-upload-doc").append(newRow);
                },
                error: function (xhr, status, error) {
                    console.error("Error fetching document:", error);
                },
                complete: function () {
                    // Reset loading state
                    $("#kode_dokumen").prop('disabled', false);
                }
            });
        }
    });

    $("#btn-add-row-upload").on("click", function () {
        let tipe = $("#tipe").val();
        let rowIndex = $("#table-upload-doc tr").length; // Hitung jumlah baris

        let photoColumn = tipe === "penempatan_lama" ? `
            <td class="text-center">
                <input type="file" name="media_new[${rowIndex}][]" class="form-control form-control-sm" accept="image/*,video/*" multiple>
                <input type="hidden" name="asset_id_new[]" class="asset-id-input">
            </td>
        ` : '';

        let newRow = `
            <tr>
                <td>
                    <select name="kode_barang[]" class="form-select dropdown-asset"></select>
                </td>
                <td></td>
                <td></td>
                ${photoColumn}
                <td class="text-center">
                    <button type="button" class="btn btn-danger btn-sm btn-remove-row-upload">
                        <i class="fa fa-times"></i>
                    </button>
                </td>
            </tr>
        `;

        let $newRow = $(newRow);
        $("#table-upload-doc").append($newRow);
        initializeSelect2Item($newRow.find(".dropdown-asset"));
    });

    $("#table-upload-doc").on("click", ".btn-remove-row-upload", function () {
        $(this).closest("tr").remove();
    });

    $("#form-upload-doc").on("keypress", function (e) {
        if (e.which === 13) {
            e.preventDefault();
        }
    });

    // File validation dipindahkan ke submit handler saja

    $(".ruangan-upload").select2({
        placeholder: "Pilih Ruangan",
        allowClear: true,
        width: "100%",
        dropdownParent: $(".ruangan-upload").parent(),
        ajax: {
            url: "/dropdown/room",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page || 1,
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.room_code + " - " + item.room_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
    });

    // Reset data when modal is opened
    $("#modal-upload-doc").on("show.bs.modal", function () {
        resetUploadForm();
    });

    // Reset data when modal is closed
    $("#modal-upload-doc").on("hidden.bs.modal", function () {
        resetUploadForm();
    });

    $("#btn-close-upload").on("click", function () {
        $("#modal-upload-doc").modal("hide");
    });

    // Minimal event handlers - error clearing dipindahkan ke submit handler saja

    // Function to reset upload form
    function resetUploadForm() {
        // Reset form fields
        $("#tipe").val("").trigger("change");
        $("#kode_dokumen").val("");
        $("#dokumen").val("");
        
        // Reset Select2 dropdowns
        if ($("#ruangan_upload").hasClass("select2-hidden-accessible")) {
            $("#ruangan_upload").val(null).trigger("change");
        }
        
        // Hide sections
        $("#document_code_field").addClass("d-none");
        $("#document_field").addClass("d-none");
        $("#ruangan_field").addClass("d-none");
        $("#asset-list-section").addClass("d-none");
        $("#table-upload").addClass("d-none");
        $("#btn-add-row-upload").addClass("d-none");
        
        // Clear table
        $("#table-upload-doc").empty();
        
        // Clear all error states and messages
        $("#form-upload-doc .is-invalid").removeClass("is-invalid");
        $("#form-upload-doc .alert").remove();
        $("#table-upload-doc").removeClass("border-danger");
        
        // Reset button state
        $("#btn-upload-doc").prop('disabled', false).html('<i class="fas fa-upload me-1"></i><span>Upload Dokumen</span>');
    }

    // QR Code click handler
    $("#datatable").on("click", ".qr-code-container", function () {
        const qrCode = $(this).data("qr-code");
        const qrCodeImage = $(this).html(); // Get the QR code image HTML

        $("#qr-code-text").val(qrCode);
        $("#qr-code-image").html(qrCodeImage);
        $("#modal-qr-code").modal("show");
    });

    // Copy QR Code functionality
    $("#copy-qr-code").on("click", function () {
        const qrCodeText = $("#qr-code-text").val();
        const $button = $(this);
        const originalText = $button.html();

        navigator.clipboard.writeText(qrCodeText).then(function () {
            // Show success message
            $button.html('<i class="fas fa-check"></i> Copied!');
            $button.removeClass('btn-outline-secondary').addClass('btn-success');

            setTimeout(function () {
                $button.html(originalText);
                $button.removeClass('btn-success').addClass('btn-outline-secondary');
            }, 2000);
        }).catch(function (err) {
            console.error('Could not copy text: ', err);
            alert('Gagal menyalin QR Code');
        });
    });
});

// CSS untuk alert styling dan dokumen icons
$(document).ready(function () {
    // Tambahkan CSS untuk alert dan dokumen icons
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .alert {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 400px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border: none;
                animation: slideInRight 0.3s ease-out;
            }
            
            .alert-success {
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                color: #155724;
                border-left: 4px solid #28a745;
            }
            
            .alert-danger {
                background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
                color: #721c24;
                border-left: 4px solid #dc3545;
            }
            
            .alert .btn-close {
                opacity: 0.7;
                transition: opacity 0.2s;
            }
            
            .alert .btn-close:hover {
                opacity: 1;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            .alert.fade-out {
                animation: slideOutRight 0.3s ease-in forwards;
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            /* Styling untuk icon dokumen */
            .datatable-modern-table .text-center i {
                font-size: 16px;
                transition: all 0.2s ease;
            }
            
            .datatable-modern-table .text-center a:hover i {
                transform: scale(1.2);
            }
            
            .datatable-modern-table .text-center i[title] {
                cursor: help;
            }
            
            .datatable-modern-table .text-center a[title] {
                cursor: pointer;
            }
            
            /* Hover effect untuk icon dokumen */
            .datatable-modern-table .text-center a:hover {
                text-decoration: none;
            }
            
            .datatable-modern-table .text-center a:hover i.text-success {
                color: #198754 !important;
                text-shadow: 0 0 8px rgba(25, 135, 84, 0.3);
            }
            
            /* Hover effect untuk icon dokumen yang tidak tersedia */
            .datatable-modern-table .text-center i.text-muted:hover {
                color: #6c757d !important;
                transform: scale(1.1);
            }
            
            /* Styling untuk icon dokumen yang tersedia */
            .datatable-modern-table .text-center i.text-success {
                cursor: pointer;
            }
            
            /* Styling untuk icon dokumen yang tidak tersedia */
            .datatable-modern-table .text-center i.text-muted {
                cursor: help;
            }
        `)
        .appendTo('head');

    // Event handler untuk tombol close alert
    $(document).on('click', '.alert .btn-close', function () {
        let $alert = $(this).closest('.alert');
        $alert.addClass('fade-out');
        setTimeout(function () {
            $alert.remove();
        }, 300);
    });
});