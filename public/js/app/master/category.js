$(document).ready(function () {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/master-data/data-kategori-list",
            data: function (d) {
                d.filter_category_type = $("#filter_category_type").val();
                d.filter_sub_category = $("#filter_sub_category").val();
            },
        },
        columns: [
            {data: "DT_RowIndex", name: "DT_RowIndex", orderable: false, searchable: false},
            {data: "category_code", name: "category_code"},
            {data: "category_name", name: "category_name"},
            {
                name: "category_type",
                data: "category_type", render: function (v) {
                    const categoryMap = {
                        "EQUIPMENT": "Peralatan",
                        "LOGISTIC": "Logistik",
                        "INFRASTRUCTURE": "Infrastruktur",
                        "OTHER": "Lainnya"
                    };
                    return categoryMap[v] || "";
                },
                searchable: false
            },
            {
                name: "category_sub_type", data: "category_sub_type", render: function (v) {
                    const categoryMap = {
                        "ALAT_KESEHATAN": "Alat Kesehatan",
                        "NON_ALAT_KESEHATAN": "Non Alat Kesehatan"
                    };
                    return categoryMap[v] || "";
                },
                searchable: false
            },
            {data: "employee_name", name: "employee_name", searchable: false},
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Add new data functionality
    $(".add-btn").on("click", function (e) {
        e.preventDefault();
        $("#kode_kategori").val("");
        $("#nama_kategori").val("");
        $("#tipe_kategori").val("");
        $("#sub_tipe_kategori").val("");
        let newOption = new Option("", "", true, true);
        $("#kategori_pic").append(newOption).trigger("change");

        $("#modal-dialog").modal("show");
        update = false;
        changeActionForm("/master-data/data-kategori/store")
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Filter category type change
    $("#filter_category_type").on("change", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Filter sub category change
    $("#filter_sub_category").on("change", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Edit functionality
    $("#datatable").on("click", ".btn-edit", function () {
        let route = $(this).data("route");
        update = true;
        changeActionForm(route);

        $.ajax({
            url: route,
            type: "GET",
            success: function (response) {
                $("#kode_kategori").val(response.data.category_code);
                $("#nama_kategori").val(response.data.category_name);
                $("#tipe_kategori").val(response.data.category_type);
                $("#sub_tipe_kategori").val(response.data.category_sub_type);
                $("#kategori_pic")
                .append(
                    new Option(
                        `${response.data.employee_identification_number} - ${response.data.employee_name}`,
                        response.data.pic_category,
                        true,
                        true,
                    ),
                )
                .trigger("change");

                $("#modal-dialog").modal("show");
            },
            error: function (xhr) {
                errorMessage(xhr.responseJSON.message);
            },
        })
    });

    // Delete functionality
    $("#datatable").on("click", ".btn-delete", function () {
        let route = $(this).data("route");

        Swal.fire({
            title: 'Apakah Anda yakin ingin menghapus data kategori ini?',
            text: "Data yang dihapus tidak dapat dikembalikan!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: route,
                    type: 'DELETE',
                    data: {
                        "_token": $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        $("#datatable").DataTable().ajax.reload();
                        successMessage(response.message || 'Data berhasil dihapus');
                    },
                    error: function(xhr) {
                        errorMessage(xhr.responseJSON.message || 'Terjadi kesalahan saat menghapus data');
                    }
                });
            }
        });
    });

    // Select2 for PIC dropdown
    $(".kategori_pic").select2({
        ajax: {
            url: "/dropdown/employee",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page || 1,
                    category: "EMPLOYEE"
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text:
                                item.employee_identification_number +
                                " - " +
                                item.employee_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih kategori",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $(".kategori_pic").parent(),
    });

    // Import Excel functionality
    $(".import-btn").on("click", function(e) {
        e.preventDefault();
        // Reset form
        $("#form-import-excel")[0].reset();
        $(".error-message").text("");
        // Reset message area
        resetImportMessageArea();
        $("#modal-import-excel").modal("show");
    });

    // Function to reset import message area
    function resetImportMessageArea() {
        $("#import-message-area").addClass("d-none");
        $("#import-success-message").addClass("d-none");
        $("#import-error-message").addClass("d-none");
        $("#success-text").text("");
        $("#error-text").text("");
        $("#error-details").html("");
    }

    // Function to show error message in modal
    function showImportErrorMessage(message, details = null) {
        $("#error-text").text(message);
        if (details) {
            $("#error-details").html(details);
        }
        $("#import-error-message").removeClass("d-none");
        $("#import-success-message").addClass("d-none");
        $("#import-message-area").removeClass("d-none");
    }

    // Handle import submit
    $("#btn-import-submit").on("click", function(e) {
        e.preventDefault();

        const fileInput = $("#excel_file")[0];
        const file = fileInput.files[0];

        // Validate file
        if (!file) {
            $("#error_excel_file").text("Silakan pilih file Excel terlebih dahulu.");
            return;
        }

        // Validate file type
        const allowedTypes = ['.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            $("#error_excel_file").text("Format file tidak didukung. Gunakan file .xlsx atau .xls");
            return;
        }

        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            $("#error_excel_file").text("Ukuran file terlalu besar. Maksimal 5MB.");
            return;
        }

        // Clear previous errors
        $(".error-message").text("");

        // Show loading state
        const originalText = $("#btn-import-submit").html();
        $("#btn-import-submit").html('<i class="fas fa-spinner fa-spin me-1"></i>Mengimport...').prop('disabled', true);

        // Create FormData
        const formData = new FormData();
        formData.append('excel_file', file);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Submit form via AJAX
        $.ajax({
            url: '/master-data/data-kategori/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // Reset loading state
                $("#btn-import-submit").html(originalText).prop('disabled', false);

                if (response.success) {
                    // Reset form
                    $("#form-import-excel")[0].reset();
                    $(".error-message").text("");

                    // Reload datatable
                    $("#datatable").DataTable().ajax.reload();

                    // Close modal
                    $("#modal-import-excel").modal("hide");

                    // Show success message using global function
                    successMessage(response.message || 'Data kategori berhasil diimport.');
                } else {
                    // Handle failed response with detailed errors
                    if (response.errors && Array.isArray(response.errors)) {
                        showDetailedImportErrors(response);
                    } else {
                        showImportErrorMessage(response.message || 'Terjadi kesalahan saat mengimport data.');
                    }
                }
            },
            error: function(xhr) {
                // Reset loading state
                $("#btn-import-submit").html(originalText).prop('disabled', false);

                let errorMessage = 'Terjadi kesalahan saat mengimport data.';

                if (xhr.responseJSON) {
                    if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    // Handle validation errors for file upload
                    if (xhr.responseJSON.errors && xhr.responseJSON.errors.excel_file) {
                        $("#error_excel_file").text(xhr.responseJSON.errors.excel_file[0]);
                        return;
                    }

                    // Handle detailed import errors
                    if (xhr.responseJSON.errors && Array.isArray(xhr.responseJSON.errors)) {
                        showDetailedImportErrors(xhr.responseJSON);
                        return;
                    }
                }

                showImportErrorMessage(errorMessage);
            }
        });
    });

    // Function to show detailed import errors
    function showDetailedImportErrors(response) {
        // Group errors by row number
        const errorsByRow = {};

        response.errors.forEach(function(error) {
            const rowNumber = error.row;
            if (!errorsByRow[rowNumber]) {
                errorsByRow[rowNumber] = {
                    values: error.values || {},
                    errors: []
                };
            }
            errorsByRow[rowNumber].errors.push({
                attribute: error.attribute,
                messages: error.errors
            });
        });

        let errorHtml = `
            <div class="text-start">
                <p><strong>Total Error:</strong> ${response.error_count || response.errors.length}</p>
                <div style="max-height: 400px; overflow-y: auto;">
        `;

        // Sort row numbers for consistent display
        const sortedRows = Object.keys(errorsByRow).sort((a, b) => parseInt(a) - parseInt(b));

        sortedRows.forEach(function(rowNumber) {
            const rowData = errorsByRow[rowNumber];
            const values = rowData.values;

            errorHtml += `
                <div class="card mb-3 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Baris ${rowNumber}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger mb-2">Error yang ditemukan:</h6>
                                <ul class="list-unstyled mb-0">
            `;

            rowData.errors.forEach(function(errorItem) {
                errorHtml += `
                                    <li class="mb-1">
                                        <strong class="text-primary">${errorItem.attribute}:</strong>
                                        <span class="text-danger">${errorItem.messages.join(', ')}</span>
                                    </li>
                `;
            });

            errorHtml += `
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info mb-2">Data pada baris ini:</h6>
                                <div class="small">
                                    <div class="mb-1"><strong>Kode:</strong> ${values.kode_kategori || '-'}</div>
                                    <div class="mb-1"><strong>Nama:</strong> ${values.nama_kategori || '-'}</div>
                                    <div class="mb-1"><strong>Tipe:</strong> ${values.tipe_kategori || '-'}</div>
                                    <div class="mb-1"><strong>Sub Tipe:</strong> ${values.sub_tipe_kategori || '-'}</div>
                                    <div class="mb-1"><strong>PIC:</strong> ${values.pic_kategori || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        errorHtml += `
                </div>
            </div>
        `;

        // Show error message with details in modal
        showImportErrorMessage('Import Gagal! Terdapat error pada data yang diimport.', errorHtml);
    }

    // Reset form when modal is hidden
    $("#modal-import-excel").on('hidden.bs.modal', function() {
        $("#form-import-excel")[0].reset();
        $(".error-message").text("");
        $("#btn-import-submit").html('<i class="fas fa-upload me-1"></i>Import').prop('disabled', false);
        // Reset message area and show form again
        resetImportMessageArea();
        $("#form-import-excel").show();
    });
});
