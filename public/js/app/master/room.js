$(document).ready(function () {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/master-data/data-ruangan-list",
            data: function (d) {
                d.room_category_id = $("#filter_room_category").val();
            },
        },
        columns: [
            {
                data: "DT_RowIndex",
                name: "DT_RowIndex",
                orderable: false,
                searchable: false,
            },
            { data: "room_code", name: "room_code" },
            { data: "room_name", name: "room_name" },
            {
                data: "division_name",
                name: "division_name",
                orderable: false,
                searchable: false,
            },
            {
                data: "room_category_name",
                name: "room_category_name",
                orderable: false,
                searchable: false,
            },
            {
                data: "building_name",
                name: "building_name",
                orderable: false,
                searchable: false,
            },
            {
                data: "room_pic_name",
                name: "room_pic_name",
                orderable: false,
                searchable: false,
            },
            { data: "device_id", name: "device_id" },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Add new data functionality
    $(".add-btn").on("click", function (e) {
        e.preventDefault();
        $("#kode_ruangan").val("");
        $("#nama_ruangan").val("");
        $("#nama_gedung").val("");
        $("#device_id").val("");

        let newOption = new Option("", "", true, true);
        $("#penanggung_jawab").append(newOption).trigger("change");

        $("#modal-dialog").modal("show");
        update = false;
        changeActionForm("/master-data/data-ruangan/store")
    });

    // Import Excel functionality
    $(".import-btn").on("click", function (e) {
        e.preventDefault();
        // Reset form
        $("#form-import-excel")[0].reset();
        $(".error-message").text("");
        // Reset message area
        resetImportMessageArea();
        $("#modal-import-excel").modal("show");
    });

    // Function to reset import message area
    function resetImportMessageArea() {
        $("#import-message-area").addClass("d-none");
        $("#import-success-message").addClass("d-none");
        $("#import-error-message").addClass("d-none");
        $("#success-text").text("");
        $("#error-text").text("");
        $("#error-details").html("");
    }

    // Function to show error message in modal
    function showImportErrorMessage(message, details = null) {
        $("#error-text").text(message);
        if (details) {
            $("#error-details").html(details);
        }
        $("#import-error-message").removeClass("d-none");
        $("#import-success-message").addClass("d-none");
        $("#import-message-area").removeClass("d-none");
    }



    // Import submit functionality
    $("#btn-import-submit").on("click", function (e) {
        e.preventDefault();

        const fileInput = $("#excel_file")[0];
        const file = fileInput.files[0];

        // Validate file
        if (!file) {
            $("#error_excel_file").text("Silakan pilih file Excel terlebih dahulu.");
            return;
        }

        // Validate file type
        const allowedTypes = ['.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            $("#error_excel_file").text("Format file tidak didukung. Gunakan file .xlsx atau .xls");
            return;
        }

        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            $("#error_excel_file").text("Ukuran file terlalu besar. Maksimal 5MB.");
            return;
        }

        // Clear previous errors
        $(".error-message").text("");

        // Show loading state
        const originalText = $("#btn-import-submit").html();
        $("#btn-import-submit").html('<i class="fas fa-spinner fa-spin me-1"></i>Mengimport...').prop('disabled', true);

        // Create FormData
        const formData = new FormData();
        formData.append('excel_file', file);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Submit form via AJAX
        $.ajax({
            url: '/master-data/data-ruangan/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // Reset loading state
                $("#btn-import-submit").html(originalText).prop('disabled', false);

                if (response.success) {
                    // Reset form
                    $("#form-import-excel")[0].reset();
                    $(".error-message").text("");

                    // Reload datatable
                    $("#datatable").DataTable().ajax.reload();

                    // Close modal
                    $("#modal-import-excel").modal("hide");

                    // Show success message using global function
                    successMessage(response.message || 'Data ruangan berhasil diimport.');
                } else {
                    // Handle failed response with detailed errors
                    if (response.errors && Array.isArray(response.errors)) {
                        showDetailedImportErrors(response);
                    } else {
                        showImportErrorMessage(response.message || 'Terjadi kesalahan saat mengimport data.');
                    }
                }
            },
            error: function(xhr) {
                // Reset loading state
                $("#btn-import-submit").html(originalText).prop('disabled', false);

                let errorMessage = 'Terjadi kesalahan saat mengimport data.';

                if (xhr.responseJSON) {
                    if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    // Handle validation errors for file upload
                    if (xhr.responseJSON.errors && xhr.responseJSON.errors.excel_file) {
                        $("#error_excel_file").text(xhr.responseJSON.errors.excel_file[0]);
                        return;
                    }

                    // Handle detailed import errors
                    if (xhr.responseJSON.errors && Array.isArray(xhr.responseJSON.errors)) {
                        showDetailedImportErrors(xhr.responseJSON);
                        return;
                    }
                }

                showImportErrorMessage(errorMessage);
            }
        });
    });

    // Function to show detailed import errors
    function showDetailedImportErrors(response) {
        // Group errors by row number
        const errorsByRow = {};

        response.errors.forEach(function(error) {
            const rowNumber = error.row;
            if (!errorsByRow[rowNumber]) {
                errorsByRow[rowNumber] = {
                    values: error.values || {},
                    errors: []
                };
            }
            errorsByRow[rowNumber].errors.push({
                attribute: error.attribute,
                messages: error.errors
            });
        });

        let errorHtml = `
            <div class="text-start">
                <p><strong>Total Error:</strong> ${response.error_count || response.errors.length}</p>
                <div style="max-height: 400px; overflow-y: auto;">
        `;

        // Sort row numbers for consistent display
        const sortedRows = Object.keys(errorsByRow).sort((a, b) => parseInt(a) - parseInt(b));

        sortedRows.forEach(function(rowNumber) {
            const rowData = errorsByRow[rowNumber];
            const values = rowData.values;

            errorHtml += `
                <div class="card mb-3 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Baris ${rowNumber}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger mb-2">Error yang ditemukan:</h6>
                                <ul class="list-unstyled mb-0">
            `;

            rowData.errors.forEach(function(errorItem) {
                errorHtml += `
                                    <li class="mb-1">
                                        <strong class="text-primary">${errorItem.attribute}:</strong>
                                        <span class="text-danger">${errorItem.messages.join(', ')}</span>
                                    </li>
                `;
            });

            errorHtml += `
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info mb-2">Data pada baris ini:</h6>
                                <div class="small">
                                    <div class="mb-1"><strong>Kode Ruangan:</strong> ${values.kode_ruangan || '-'}</div>
                                    <div class="mb-1"><strong>Nama Ruangan:</strong> ${values.nama_ruangan || '-'}</div>
                                    <div class="mb-1"><strong>Nama Gedung:</strong> ${values.nama_gedung || '-'}</div>
                                    <div class="mb-1"><strong>Kategori Ruangan:</strong> ${values.kategori_ruangan || '-'}</div>
                                    <div class="mb-1"><strong>Penanggung Jawab:</strong> ${values.penanggung_jawab || '-'}</div>
                                    <div class="mb-1"><strong>Divisi:</strong> ${values.divisi || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        errorHtml += `
                </div>
            </div>
        `;

        // Show error message with details in modal
        showImportErrorMessage('Import Gagal! Terdapat error pada data yang diimport.', errorHtml);
    }

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Filter room category change
    $("#filter_room_category").on("change", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Edit functionality
    $("#datatable").on("click", ".btn-edit", function () {
        let route = $(this).data("route");
        update = true;
        changeActionForm(route);

        $.ajax({
            url: route,
            type: "GET",
            success: function (response) {
                $("#kode_ruangan").val(response.data.room_code);
                $("#nama_ruangan").val(response.data.room_name);
                $("#nama_gedung").val(response.data.building_name);
                $("#device_id").val(response.data.device_id);
                $("#division_id").val(response.data.division_id);
                $("#kategori-ruangan").val(response.data.room_category);

                $("#penanggung_jawab")
                    .append(
                        new Option(
                            `${response.data.employee_identification_number} - ${response.data.employee_name}`,
                            response.data.pic_room,
                            true,
                            true,
                        ),
                    )
                    .trigger("change");

                $("#modal-dialog").modal("show");
            },
            error: function (xhr) {
                errorMessage(xhr.responseJSON.message);
            },
        })
    });

    // Select2 for PIC dropdown
    $(".penanggung_jawab").select2({
        ajax: {
            url: "/dropdown/employee",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page || 1,
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text:
                                item.employee_identification_number +
                                " - " +
                                item.employee_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih Penanggung Jawab",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $(".penanggung_jawab").parent(),
    });

    // Load room categories for filter dropdown
    $.ajax({
        url: "/dropdown/room-category",
        type: "GET",
        success: function (response) {
            response.forEach(function (category) {
                $("#filter_room_category").append(
                    `<option value="${category.id}">${category.room_category_code} - ${category.room_category_name}</option>`,
                );
            });
        },
        error: function (xhr) {
            console.error("Error fetching room categories:", xhr.responseText);
        },
    });

    // Load room categories for form dropdown
    $.ajax({
        url: "/dropdown/room-category",
        type: "GET",
        success: function (response) {
            response.forEach(function (category) {
                $("#kategori-ruangan").append(
                    `<option value="${category.id}">${category.room_category_code} - ${category.room_category_name}</option>`,
                );
            });
        },
        error: function (xhr) {
            console.error("Error fetching room categories:", xhr.responseText);
        },
    });
});
